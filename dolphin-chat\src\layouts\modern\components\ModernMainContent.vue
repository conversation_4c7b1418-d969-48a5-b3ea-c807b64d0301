<template>
  <div class="modern-main-content">
    <!-- 欢迎页面 -->
    <div v-if="showWelcome" class="welcome-container">
      <ModernWelcomePage />
    </div>

    <!-- 聊天页面 -->
    <div v-else class="chat-container">
      <ModernChatPage />
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { useChatStore } from '@/stores/baseStore'
import ModernWelcomePage from './ModernWelcomePage.vue'
import ModernChatPage from './ModernChatPage.vue'

// 使用聊天store
const chatStore = useChatStore()

// 计算属性
const showWelcome = computed(() => {
  return chatStore.showWelcome || chatStore.currentMessages.length === 0
})
</script>

<style scoped>
.modern-main-content {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.welcome-container,
.chat-container {
  flex: 1;
  display: flex;
  flex-direction: column;
}
</style>
