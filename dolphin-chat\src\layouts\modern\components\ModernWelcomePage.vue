<template>
  <div class="modern-welcome">
    <div class="welcome-content">
      <!-- 主标题区域 -->
      <div class="hero-section">
        <div class="hero-icon">
          <VIcon size="48" color="#3b82f6">mdi-robot-outline</VIcon>
        </div>
        <h1 class="hero-title">欢迎使用 Dolphin AI</h1>
        <p class="hero-subtitle">智能对话助手，让交流更高效</p>
      </div>

      <!-- 功能卡片 -->
      <div class="features-grid">
        <div class="feature-card" @click="handleQuickStart('写作助手')">
          <div class="feature-icon">
            <VIcon size="24" color="#3b82f6">mdi-pencil</VIcon>
          </div>
          <h3 class="feature-title">写作助手</h3>
          <p class="feature-desc">帮助您创作文章、邮件和各种文档</p>
        </div>

        <div class="feature-card" @click="handleQuickStart('代码助手')">
          <div class="feature-icon">
            <VIcon size="24" color="#3b82f6">mdi-code-tags</VIcon>
          </div>
          <h3 class="feature-title">代码助手</h3>
          <p class="feature-desc">编程问题解答和代码优化建议</p>
        </div>

        <div class="feature-card" @click="handleQuickStart('学习助手')">
          <div class="feature-icon">
            <VIcon size="24" color="#3b82f6">mdi-school</VIcon>
          </div>
          <h3 class="feature-title">学习助手</h3>
          <p class="feature-desc">知识问答和学习指导</p>
        </div>

        <div class="feature-card" @click="handleQuickStart('翻译助手')">
          <div class="feature-icon">
            <VIcon size="24" color="#3b82f6">mdi-translate</VIcon>
          </div>
          <h3 class="feature-title">翻译助手</h3>
          <p class="feature-desc">多语言翻译和语言学习</p>
        </div>
      </div>

      <!-- 快速开始提示 -->
      <div class="quick-start">
        <div class="start-hint">
          <VIcon size="20" color="#6b7280">mdi-lightbulb-outline</VIcon>
          <span>点击上方卡片快速开始，或在下方输入框直接提问</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { useChatStore } from '@/stores/baseStore'

const chatStore = useChatStore()

// 处理快速开始
const handleQuickStart = (type) => {
  const prompts = {
    '写作助手': '你好！我需要写作方面的帮助，请问你能协助我什么？',
    '代码助手': '你好！我在编程方面遇到了问题，你能帮助我吗？',
    '学习助手': '你好！我想学习新知识，你能给我一些指导吗？',
    '翻译助手': '你好！我需要翻译帮助，你支持哪些语言？'
  }
  
  // 设置预设消息到输入框
  chatStore.setPresetMessage(prompts[type])
  
  // 隐藏欢迎页面
  chatStore.setShowWelcome(false)
}
</script>

<style scoped>
.modern-welcome {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 100%;
  padding: 40px 20px;
}

.welcome-content {
  max-width: 800px;
  width: 100%;
  text-align: center;
}

/* 主标题区域 */
.hero-section {
  margin-bottom: 48px;
}

.hero-icon {
  margin-bottom: 24px;
}

.hero-title {
  font-size: 32px;
  font-weight: 700;
  color: #1f2937;
  margin: 0 0 12px 0;
  letter-spacing: -0.025em;
}

.hero-subtitle {
  font-size: 18px;
  color: #6b7280;
  margin: 0;
  font-weight: 400;
}

/* 功能卡片网格 */
.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 20px;
  margin-bottom: 40px;
}

.feature-card {
  background: white;
  border-radius: 12px;
  padding: 24px;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid rgba(0, 0, 0, 0.06);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.feature-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.15);
  border-color: rgba(59, 130, 246, 0.2);
}

.feature-icon {
  width: 48px;
  height: 48px;
  background: rgba(59, 130, 246, 0.1);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 16px;
}

.feature-title {
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 8px 0;
}

.feature-desc {
  font-size: 14px;
  color: #6b7280;
  margin: 0;
  line-height: 1.5;
}

/* 快速开始提示 */
.quick-start {
  margin-top: 32px;
}

.start-hint {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  background: rgba(59, 130, 246, 0.05);
  border: 1px solid rgba(59, 130, 246, 0.1);
  border-radius: 24px;
  font-size: 14px;
  color: #6b7280;
}

/* 深色主题适配 */
.theme--dark .hero-title {
  color: #f9fafb;
}

.theme--dark .hero-subtitle {
  color: #9ca3af;
}

.theme--dark .feature-card {
  background: #1f2937;
  border-color: rgba(255, 255, 255, 0.1);
}

.theme--dark .feature-card:hover {
  border-color: rgba(59, 130, 246, 0.3);
}

.theme--dark .feature-title {
  color: #f9fafb;
}

.theme--dark .feature-desc {
  color: #9ca3af;
}

.theme--dark .start-hint {
  background: rgba(59, 130, 246, 0.1);
  border-color: rgba(59, 130, 246, 0.2);
  color: #9ca3af;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .modern-welcome {
    padding: 20px 16px;
  }
  
  .hero-title {
    font-size: 28px;
  }
  
  .hero-subtitle {
    font-size: 16px;
  }
  
  .features-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }
  
  .feature-card {
    padding: 20px;
  }
  
  .start-hint {
    padding: 10px 16px;
    font-size: 13px;
  }
}
</style>
