<template>
  <VApp class="modern-app">
    <!-- 顶部导航栏 -->
    <VAppBar 
      :elevation="0" 
      class="top-nav-bar"
      height="64"
      color="transparent">
      <div class="nav-container">
        <!-- 左侧：Logo和品牌 -->
        <div class="nav-left">
          <div class="brand-section" @click="handleLogoClick">
            <img 
              :src="logoUrl" 
              alt="Dolphin AI" 
              class="brand-logo"
            />
            <span class="brand-text">Dolphin AI</span>
          </div>
        </div>

        <!-- 中间：对话标题 -->
        <div class="nav-center">
          <div class="conversation-title">
            {{ currentConversationTitle }}
          </div>
        </div>

        <!-- 右侧：操作按钮 -->
        <div class="nav-right">
          <!-- 新建对话 -->
          <VBtn
            icon
            variant="text"
            class="nav-btn"
            @click="handleNewChat">
            <VIcon>mdi-plus</VIcon>
            <VTooltip activator="parent" location="bottom">新建对话</VTooltip>
          </VBtn>

          <!-- 对话历史 -->
          <VBtn
            icon
            variant="text"
            class="nav-btn"
            @click="showHistoryDrawer = true">
            <VIcon>mdi-history</VIcon>
            <VTooltip activator="parent" location="bottom">对话历史</VTooltip>
          </VBtn>

          <!-- 模型选择 -->
          <VBtn
            icon
            variant="text"
            class="nav-btn"
            @click="showModelSelector = true">
            <VIcon>mdi-robot</VIcon>
            <VTooltip activator="parent" location="bottom">选择模型</VTooltip>
          </VBtn>

          <!-- 设置菜单 -->
          <VMenu>
            <template #activator="{ props }">
              <VBtn
                icon
                variant="text"
                class="nav-btn"
                v-bind="props">
                <VIcon>mdi-dots-vertical</VIcon>
              </VBtn>
            </template>
            <VList class="settings-menu">
              <VListItem @click="showSettings = true">
                <VListItemTitle>系统设置</VListItemTitle>
                <template #prepend>
                  <VIcon>mdi-cog</VIcon>
                </template>
              </VListItem>
              <VListItem @click="handleLogout">
                <VListItemTitle>退出登录</VListItemTitle>
                <template #prepend>
                  <VIcon>mdi-logout</VIcon>
                </template>
              </VListItem>
            </VList>
          </VMenu>
        </div>
      </div>
    </VAppBar>

    <!-- 主内容区域 -->
    <VMain class="main-content">
      <div class="chat-container">
        <!-- 聊天内容区域 -->
        <div class="chat-content" ref="chatContainer">
          <ModernMainContent />
        </div>

        <!-- 聊天输入区域 -->
        <div class="chat-input-area">
          <ChatInput />
        </div>
      </div>
    </VMain>

    <!-- 对话历史抽屉 -->
    <VNavigationDrawer
      v-model="showHistoryDrawer"
      location="left"
      temporary
      width="320"
      class="history-drawer">
      <div class="drawer-header">
        <h3>对话历史</h3>
        <VBtn
          icon
          variant="text"
          size="small"
          @click="showHistoryDrawer = false">
          <VIcon>mdi-close</VIcon>
        </VBtn>
      </div>
      <ConversationHistory @conversation-select="handleConversationSelect" />
    </VNavigationDrawer>

    <!-- 模型选择弹窗 -->
    <VDialog v-model="showModelSelector" max-width="400">
      <VCard class="model-selector-card">
        <VCardTitle>选择模型</VCardTitle>
        <VCardText>
          <ModelSelector />
        </VCardText>
        <VCardActions>
          <VSpacer />
          <VBtn @click="showModelSelector = false">关闭</VBtn>
        </VCardActions>
      </VCard>
    </VDialog>

    <!-- 系统设置弹窗 -->
    <SystemSettingsDialog v-model="showSettings" />

    <!-- 全局加载弹窗 -->
    <GlobalLoadingDialog
      v-model="chatStore.showGlobalLoading"
      :loading-text="chatStore.globalLoadingText"
      :sub-text="chatStore.globalLoadingSubText"
      :show-progress="chatStore.showGlobalProgress"
      :progress="chatStore.globalLoadingProgress" />
  </VApp>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { useChatStore } from '@/stores/baseStore'
import { useAuthStore } from '@/stores/authstore'

// 导入组件
import ModernMainContent from './components/ModernMainContent.vue'
import ChatInput from '@/pages/chat/components/ChatInput/index.vue'
import ConversationHistory from '@/pages/conversation/ConversationHistory.vue'
import ModelSelector from '@/components/common/ModelSelector.vue'
import SystemSettingsDialog from '@/pages/settings/SystemSettingsDialog.vue'
import GlobalLoadingDialog from '@/components/common/GlobalLoadingDialog.vue'

// 导入资源
import logoUrl from '@/assets/logo.png'

// 状态管理
const chatStore = useChatStore()
const authStore = useAuthStore()
const router = useRouter()

// 响应式数据
const showHistoryDrawer = ref(false)
const showModelSelector = ref(false)
const showSettings = ref(false)
const chatContainer = ref(null)

// 计算属性
const currentConversationTitle = computed(() => {
  return chatStore.currentConversationTitle || '新对话'
})

// 方法
const handleLogoClick = () => {
  router.push({ name: 'home' })
}

const handleNewChat = async () => {
  await chatStore.createNewSession()
  showHistoryDrawer.value = false
}

const handleConversationSelect = (conversation) => {
  chatStore.selectConversation(conversation)
  showHistoryDrawer.value = false
}

const handleLogout = async () => {
  await authStore.logout()
  router.push({ name: 'login' })
}

// 生命周期
onMounted(() => {
  // 初始化
})

onUnmounted(() => {
  // 清理
})
</script>

<style scoped>
.modern-app {
  background: var(--app-bg-primary);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* 顶部导航栏样式 */
.top-nav-bar {
  background: rgba(255, 255, 255, 0.95) !important;
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(0, 0, 0, 0.06);
}

.nav-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
}

.nav-left, .nav-right {
  display: flex;
  align-items: center;
  gap: 8px;
}

.nav-center {
  flex: 1;
  display: flex;
  justify-content: center;
}

/* 品牌区域 */
.brand-section {
  display: flex;
  align-items: center;
  gap: 12px;
  cursor: pointer;
  padding: 8px 12px;
  border-radius: 8px;
  transition: background-color 0.2s ease;
}

.brand-section:hover {
  background-color: rgba(59, 130, 246, 0.08);
}

.brand-logo {
  width: 32px;
  height: 32px;
  border-radius: 6px;
}

.brand-text {
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
  letter-spacing: -0.025em;
}

/* 对话标题 */
.conversation-title {
  font-size: 16px;
  font-weight: 500;
  color: #374151;
  max-width: 300px;
  text-align: center;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 导航按钮 */
.nav-btn {
  color: #6b7280 !important;
  transition: all 0.2s ease;
}

.nav-btn:hover {
  color: #3b82f6 !important;
  background-color: rgba(59, 130, 246, 0.08) !important;
}

/* 主内容区域 */
.main-content {
  padding: 0;
  background: #fafafa;
}

.chat-container {
  display: flex;
  flex-direction: column;
  height: calc(100vh - 64px);
  max-width: 1000px;
  margin: 0 auto;
}

.chat-content {
  flex: 1;
  overflow-y: auto;
  padding: 24px;
}

.chat-input-area {
  padding: 16px 24px 24px;
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  border-top: 1px solid rgba(0, 0, 0, 0.06);
}

/* 抽屉样式 */
.history-drawer {
  background: white;
}

.drawer-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 20px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.06);
}

.drawer-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
}

/* 设置菜单 */
.settings-menu {
  min-width: 160px;
}

/* 模型选择卡片 */
.model-selector-card {
  border-radius: 12px;
}

/* 深色主题适配 */
.theme--dark .top-nav-bar {
  background: rgba(31, 41, 55, 0.95) !important;
  border-bottom-color: rgba(255, 255, 255, 0.1);
}

.theme--dark .brand-text {
  color: #f9fafb;
}

.theme--dark .conversation-title {
  color: #d1d5db;
}

.theme--dark .main-content {
  background: #111827;
}

.theme--dark .chat-input-area {
  background: rgba(31, 41, 55, 0.8);
  border-top-color: rgba(255, 255, 255, 0.1);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .nav-container {
    padding: 0 16px;
  }
  
  .brand-text {
    display: none;
  }
  
  .conversation-title {
    max-width: 200px;
    font-size: 14px;
  }
  
  .chat-content {
    padding: 16px;
  }
  
  .chat-input-area {
    padding: 12px 16px 16px;
  }
}
</style>
